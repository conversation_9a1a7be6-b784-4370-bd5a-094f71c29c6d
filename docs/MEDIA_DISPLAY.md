# MediaDisplay Component

The `MediaDisplay` component is a flexible media renderer that can handle various types of content including images, videos, and embedded social media posts.

## Features

The component automatically detects the media type based on the URL and renders the appropriate content:

### Supported Media Types

1. **Images** (default)
   - Standard image files (jpg, png, gif, webp, etc.)
   - Fallback for any unrecognized URL format

2. **Videos**
   - **YouTube**: `youtube.com/watch?v=` or `youtu.be/`
   - **Vimeo**: `vimeo.com/`
   - **Direct video files**: `.mp4`, `.webm`, `.ogg`, `.mov`, `.avi`

3. **Audio Files**
   - Direct audio files: `.mp3`, `.wav`, `.ogg`, `.aac`, `.flac`

4. **Social Media Embeds**
   - **Instagram**: Posts and Reels (`instagram.com/p/` or `instagram.com/reel/`)
   - **TikTok**: Videos (`tiktok.com/`)
   - **Twitter/X**: Tweets (`twitter.com/` or `x.com/`)

## Usage

### Basic Usage

```tsx
import MediaDisplay from '@/components/MediaDisplay'

// Image
<MediaDisplay 
  src="/path/to/image.jpg" 
  alt="Course preview" 
  width={600} 
  height={400} 
/>

// YouTube Video
<MediaDisplay 
  src="https://www.youtube.com/watch?v=dQw4w9WgXcQ" 
  alt="Course introduction video" 
  width={600} 
  height={400} 
/>

// Instagram Post
<MediaDisplay 
  src="https://www.instagram.com/p/ABC123/" 
  alt="Course highlights" 
  width={600} 
  height={400} 
/>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `src` | `string` | - | **Required.** URL or path to the media content |
| `alt` | `string` | - | **Required.** Alternative text for accessibility |
| `width` | `number` | `600` | Width of the media container |
| `height` | `number` | `400` | Height of the media container |
| `className` | `string` | `""` | Additional CSS classes |

## Examples

### Course Data Structure

Update your course data to include various media types:

```typescript
const courses = {
  'example-course': {
    // ... other course properties
    
    // Image (traditional)
    image: '/images/course-preview.jpg',
    
    // YouTube video
    image: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    
    // Instagram post
    image: 'https://www.instagram.com/p/ABC123/',
    
    // Vimeo video
    image: 'https://vimeo.com/123456789',
    
    // Direct video file
    image: '/videos/course-intro.mp4',
    
    // Audio file
    image: '/audio/course-intro.mp3',
  }
}
```

### URL Format Examples

#### YouTube
- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`

#### Vimeo
- `https://vimeo.com/VIDEO_ID`

#### Instagram
- `https://www.instagram.com/p/POST_ID/`
- `https://www.instagram.com/reel/REEL_ID/`

#### TikTok
- `https://www.tiktok.com/@username/video/VIDEO_ID`

#### Twitter/X
- `https://twitter.com/username/status/TWEET_ID`
- `https://x.com/username/status/TWEET_ID`

## Implementation Details

### Auto-Detection Logic

The component uses URL pattern matching to determine the media type:

1. **Instagram**: Checks for `instagram.com/p/` or `instagram.com/reel/`
2. **YouTube**: Checks for `youtube.com/watch` or `youtu.be/`
3. **Vimeo**: Checks for `vimeo.com/`
4. **TikTok**: Checks for `tiktok.com/`
5. **Twitter/X**: Checks for `twitter.com/` or `x.com/`
6. **Video files**: Regex match for video file extensions
7. **Audio files**: Regex match for audio file extensions
8. **Images**: Default fallback for all other URLs

### Responsive Design

All embedded content is responsive and includes:
- Rounded corners (`rounded-xl`)
- Proper aspect ratios
- Mobile-friendly sizing

### Accessibility

- All embeds include proper `title` attributes
- Images include `alt` text
- External links include `rel="noopener noreferrer"`

## Browser Compatibility

- Modern browsers with iframe support
- JavaScript required for social media embeds
- Fallback links provided for unsupported content

## Security Considerations

- All external embeds are sandboxed
- No direct script execution from user content
- Social media embeds use official embed APIs
