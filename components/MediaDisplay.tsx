import Image from 'next/image'

// Media Component for handling different types of content
interface MediaDisplayProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
}

export default function MediaDisplay({ src, alt, width = 600, height = 400, className = "" }: MediaDisplayProps) {
  // Check if the src is an Instagram embed URL
  if (src.includes('instagram.com/p/') || src.includes('instagram.com/reel/')) {
    const embedUrl = src.replace('/p/', '/embed/').replace('/reel/', '/embed/')
    return (
      <div className={`relative ${className}`} style={{ width, height }}>
        <iframe
          src={embedUrl}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          className="rounded-xl"
          title={alt}
        />
      </div>
    )
  }

  // Check if the src is a YouTube URL
  if (src.includes('youtube.com/watch') || src.includes('youtu.be/')) {
    let videoId = ''
    if (src.includes('youtube.com/watch')) {
      videoId = src.split('v=')[1]?.split('&')[0] || ''
    } else if (src.includes('youtu.be/')) {
      videoId = src.split('youtu.be/')[1]?.split('?')[0] || ''
    }
    
    if (videoId) {
      return (
        <div className={`relative ${className}`} style={{ width, height }}>
          <iframe
            src={`https://www.youtube.com/embed/${videoId}`}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="rounded-xl"
            title={alt}
          />
        </div>
      )
    }
  }

  // Check if the src is a Vimeo URL
  if (src.includes('vimeo.com/')) {
    const videoId = src.split('vimeo.com/')[1]?.split('?')[0] || ''
    if (videoId) {
      return (
        <div className={`relative ${className}`} style={{ width, height }}>
          <iframe
            src={`https://player.vimeo.com/video/${videoId}`}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allow="autoplay; fullscreen; picture-in-picture"
            allowFullScreen
            className="rounded-xl"
            title={alt}
          />
        </div>
      )
    }
  }

  // Check if the src is a TikTok URL
  if (src.includes('tiktok.com/')) {
    return (
      <div className={`relative ${className}`} style={{ width, height }}>
        <blockquote 
          className="tiktok-embed rounded-xl" 
          cite={src} 
          data-video-id={src.split('/video/')[1]?.split('?')[0] || ''}
          style={{ maxWidth: '605px', minWidth: '325px' }}
        >
          <section>
            <a target="_blank" title={alt} href={src} rel="noopener noreferrer">
              View on TikTok
            </a>
          </section>
        </blockquote>
        <script async src="https://www.tiktok.com/embed.js"></script>
      </div>
    )
  }

  // Check if the src is a Twitter/X URL
  if (src.includes('twitter.com/') || src.includes('x.com/')) {
    return (
      <div className={`relative ${className}`} style={{ width, height }}>
        <blockquote className="twitter-tweet rounded-xl">
          <a href={src} target="_blank" rel="noopener noreferrer">
            View Tweet
          </a>
        </blockquote>
        <script async src="https://platform.twitter.com/widgets.js"></script>
      </div>
    )
  }

  // Check if the src is a video file
  if (src.match(/\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i)) {
    return (
      <div className={`relative ${className}`}>
        <video
          width={width}
          height={height}
          controls
          className="rounded-xl w-full"
          poster="/placeholder.svg"
        >
          <source src={src} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
    )
  }

  // Check if the src is an audio file
  if (src.match(/\.(mp3|wav|ogg|aac|flac)(\?.*)?$/i)) {
    return (
      <div className={`relative ${className} bg-gray-800 rounded-xl p-8 flex items-center justify-center`} style={{ width, height }}>
        <audio controls className="w-full">
          <source src={src} />
          Your browser does not support the audio tag.
        </audio>
      </div>
    )
  }

  // Default to image for everything else
  return (
    <Image
      src={src || "/placeholder.svg"}
      alt={alt}
      width={width}
      height={height}
      className={`rounded-xl w-full ${className}`}
    />
  )
}
