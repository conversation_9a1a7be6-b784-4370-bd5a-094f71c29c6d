import Image from 'next/image'

// Media Component for handling different types of content
interface MediaDisplayProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
}

export default function MediaDisplay({ src, alt, width = 600, height = 400, className = "" }: MediaDisplayProps) {
  // Check if the src is an Instagram embed URL
  if (src.includes('instagram.com/p/') || src.includes('instagram.com/reel/')) {
    return (
      <div className={`relative ${className} overflow-hidden rounded-xl`} style={{ width: '100%', maxWidth: '540px' }}>
        <div
          className="bg-white border border-gray-200 rounded-xl shadow-sm"
          style={{ maxWidth: '540px', minWidth: '326px' }}
        >
          {/* Instagram Header */}
          <div className="flex items-center p-4">
            <div className="w-8 h-8 bg-gray-200 rounded-full mr-3"></div>
            <div className="flex-1">
              <div className="h-3 bg-gray-200 rounded mb-1" style={{ width: '120px' }}></div>
              <div className="h-2 bg-gray-200 rounded" style={{ width: '80px' }}></div>
            </div>
          </div>

          {/* Instagram Content Area */}
          <div className="relative bg-gray-100 aspect-square">
            <div className="absolute inset-0 flex items-center justify-center">
              <svg width="50px" height="50px" viewBox="0 0 60 60" className="text-gray-400">
                <g stroke="none" strokeWidth="1" fill="currentColor" fillRule="evenodd">
                  <g transform="translate(-511.000000, -20.000000)">
                    <g>
                      <path d="M556.869,30.41 C554.814,30.41 553.148,32.076 553.148,34.131 C553.148,36.186 554.814,37.852 556.869,37.852 C558.924,37.852 560.59,36.186 560.59,34.131 C560.59,32.076 558.924,30.41 556.869,30.41 M541,60.657 C535.114,60.657 530.342,55.887 530.342,50 C530.342,44.114 535.114,39.342 541,39.342 C546.887,39.342 551.658,44.114 551.658,50 C551.658,55.887 546.887,60.657 541,60.657 M541,33.886 C532.1,33.886 524.886,41.1 524.886,50 C524.886,58.899 532.1,66.113 541,66.113 C549.9,66.113 557.115,58.899 557.115,50 C557.115,41.1 549.9,33.886 541,33.886 M565.378,62.101 C565.244,65.022 564.756,66.606 564.346,67.663 C563.803,69.06 563.154,70.057 562.106,71.106 C561.058,72.155 560.06,72.803 558.662,73.347 C557.607,73.757 556.021,74.244 553.102,74.378 C549.944,74.521 548.997,74.552 541,74.552 C533.003,74.552 532.056,74.521 528.898,74.378 C525.979,74.244 524.393,73.757 523.338,73.347 C521.94,72.803 520.942,72.155 519.894,71.106 C518.846,70.057 518.197,69.06 517.654,67.663 C517.244,66.606 516.755,65.022 516.623,62.101 C516.479,58.943 516.448,57.996 516.448,50 C516.448,42.003 516.479,41.056 516.623,37.899 C516.755,34.978 517.244,33.391 517.654,32.338 C518.197,30.938 518.846,29.942 519.894,28.894 C520.942,27.846 521.94,27.196 523.338,26.654 C524.393,26.244 525.979,25.756 528.898,25.623 C532.057,25.479 533.004,25.448 541,25.448 C548.997,25.448 549.943,25.479 553.102,25.623 C556.021,25.756 557.607,26.244 558.662,26.654 C560.06,27.196 561.058,27.846 562.106,28.894 C563.154,29.942 563.803,30.938 564.346,32.338 C564.756,33.391 565.244,34.978 565.378,37.899 C565.522,41.056 565.552,42.003 565.552,50 C565.552,57.996 565.522,58.943 565.378,62.101 M570.82,37.631 C570.674,34.438 570.167,32.258 569.425,30.349 C568.659,28.377 567.633,26.702 565.965,25.035 C564.297,23.368 562.623,22.342 560.652,21.575 C558.743,20.834 556.562,20.326 553.369,20.18 C550.169,20.033 549.148,20 541,20 C532.853,20 531.831,20.033 528.631,20.18 C525.438,20.326 523.257,20.834 521.349,21.575 C519.376,22.342 517.703,23.368 516.035,25.035 C514.368,26.702 513.342,28.377 512.574,30.349 C511.834,32.258 511.326,34.438 511.181,37.631 C511.035,40.831 511,41.851 511,50 C511,58.147 511.035,59.17 511.181,62.369 C511.326,65.562 511.834,67.743 512.574,69.651 C513.342,71.625 514.368,73.296 516.035,74.965 C517.703,76.634 519.376,77.658 521.349,78.425 C523.257,79.167 525.438,79.673 528.631,79.82 C531.831,79.965 532.853,80.001 541,80.001 C549.148,80.001 550.169,79.965 553.369,79.82 C556.562,79.673 558.743,79.167 560.652,78.425 C562.623,77.658 564.297,76.634 565.965,74.965 C567.633,73.296 568.659,71.625 569.425,69.651 C570.167,67.743 570.674,65.562 570.82,62.369 C570.966,59.17 571,58.147 571,50 C571,41.851 570.966,40.831 570.82,37.631"></path>
                    </g>
                  </g>
                </g>
              </svg>
            </div>
          </div>

          {/* Instagram Actions */}
          <div className="p-4">
            <div className="flex items-center space-x-4 mb-2">
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
            </div>
            <div className="h-3 bg-gray-200 rounded mb-2" style={{ width: '60px' }}></div>
            <a
              href={src}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 text-sm hover:underline"
            >
              View more on Instagram
            </a>
          </div>
        </div>
      </div>
    )
  }

  // Check if the src is a YouTube URL
  if (src.includes('youtube.com/watch') || src.includes('youtu.be/')) {
    let videoId = ''
    if (src.includes('youtube.com/watch')) {
      videoId = src.split('v=')[1]?.split('&')[0] || ''
    } else if (src.includes('youtu.be/')) {
      videoId = src.split('youtu.be/')[1]?.split('?')[0] || ''
    }
    
    if (videoId) {
      return (
        <div className={`relative ${className}`} style={{ width, height }}>
          <iframe
            src={`https://www.youtube.com/embed/${videoId}`}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="rounded-xl"
            title={alt}
          />
        </div>
      )
    }
  }

  // Check if the src is a Vimeo URL
  if (src.includes('vimeo.com/')) {
    const videoId = src.split('vimeo.com/')[1]?.split('?')[0] || ''
    if (videoId) {
      return (
        <div className={`relative ${className}`} style={{ width, height }}>
          <iframe
            src={`https://player.vimeo.com/video/${videoId}`}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allow="autoplay; fullscreen; picture-in-picture"
            allowFullScreen
            className="rounded-xl"
            title={alt}
          />
        </div>
      )
    }
  }

  // Check if the src is a TikTok URL
  if (src.includes('tiktok.com/')) {
    return (
      <div className={`relative ${className}`} style={{ width, height }}>
        <blockquote 
          className="tiktok-embed rounded-xl" 
          cite={src} 
          data-video-id={src.split('/video/')[1]?.split('?')[0] || ''}
          style={{ maxWidth: '605px', minWidth: '325px' }}
        >
          <section>
            <a target="_blank" title={alt} href={src} rel="noopener noreferrer">
              View on TikTok
            </a>
          </section>
        </blockquote>
        <script async src="https://www.tiktok.com/embed.js"></script>
      </div>
    )
  }

  // Check if the src is a Twitter/X URL
  if (src.includes('twitter.com/') || src.includes('x.com/')) {
    return (
      <div className={`relative ${className}`} style={{ width, height }}>
        <blockquote className="twitter-tweet rounded-xl">
          <a href={src} target="_blank" rel="noopener noreferrer">
            View Tweet
          </a>
        </blockquote>
        <script async src="https://platform.twitter.com/widgets.js"></script>
      </div>
    )
  }

  // Check if the src is a video file
  if (src.match(/\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i)) {
    return (
      <div className={`relative ${className}`}>
        <video
          width={width}
          height={height}
          controls
          className="rounded-xl w-full"
          poster="/placeholder.svg"
        >
          <source src={src} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
    )
  }

  // Check if the src is an audio file
  if (src.match(/\.(mp3|wav|ogg|aac|flac)(\?.*)?$/i)) {
    return (
      <div className={`relative ${className} bg-gray-800 rounded-xl p-8 flex items-center justify-center`} style={{ width, height }}>
        <audio controls className="w-full">
          <source src={src} />
          Your browser does not support the audio tag.
        </audio>
      </div>
    )
  }

  // Default to image for everything else
  return (
    <Image
      src={src || "/placeholder.svg"}
      alt={alt}
      width={width}
      height={height}
      className={`rounded-xl w-full ${className}`}
    />
  )
}
